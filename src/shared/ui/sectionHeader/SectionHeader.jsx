import React, { useRef, useState, useLayoutEffect, useEffect, useMemo } from "react";

// Hook to measure container and text widths
function useDimensions() {
    const containerRef = useRef(null);
    const textRef = useRef(null);
    const [dims, setDims] = useState({ fullW: 0, textW: 0 });

    useLayoutEffect(() => {
        function measure() {
            if (!containerRef.current || !textRef.current) return;
            setDims({
                fullW: containerRef.current.clientWidth,
                textW: textRef.current.clientWidth,
            });
        }
        measure();
        window.addEventListener("resize", measure);
        return () => window.removeEventListener("resize", measure);
    }, []);

    return { containerRef, textRef, ...dims };
}

// Deterministic pseudo-random generator
function mulberry32(seed) {
    return function () {
        let t = (seed += 0x6d2b79f5);
        t = Math.imul(t ^ (t >>> 15), t | 1);
        t ^= t + Math.imul(t ^ (t >>> 7), t | 61);
        return ((t ^ (t >>> 14)) >>> 0) / 4294967296;
    };
}

// Compute SVG path for header underline with rounded-corner humps
function generatePathData({
    fullW,
    textW,
    lineY,
    dropHeight,
    textGap,
    rightGap,
    radius,
    humpCount,
    seed = 0.1283918237192387912837,
}) {
    const rand = mulberry32(seed);

    const y1 = lineY;
    const adjustedDrop = dropHeight + textGap * 2;
    const y2 = y1 + adjustedDrop;

    const startX = textW + textGap + rightGap;
    const segs = [
        `M0,${y1}`,
        `L${startX - radius},${y1}`,
        `A${radius},${radius} 0 0,1 ${startX},${y1 + radius}`,
        `L${startX},${y2 - radius}`,
        `A${radius},${radius} 0 0,0 ${startX + radius},${y2}`,
    ];

    const tailExtension = rand() * 105;

    const areaStart = startX + radius;
    const areaEnd = fullW - tailExtension - radius;
    const availableWidth = areaEnd - areaStart;
    const segmentWidth = availableWidth / humpCount;

    for (let i = 0; i < humpCount; i++) {
        const segStart = areaStart + i * segmentWidth;
        const minSize = Math.min(adjustedDrop * 0.2, segmentWidth * 0.2);
        const maxSize = Math.min(adjustedDrop, segmentWidth);
        const size = rand() * (maxSize - minSize) + minSize;
        const hr = Math.min(radius, size / 4);
        const maxOffset = Math.max(0, segmentWidth - size);
        const offset = rand() * maxOffset;
        const x0 = segStart + offset;
        const x1 = x0 + size;

        segs.push(`L${x0 - hr},${y2}`);
        segs.push(`A${hr},${hr} 0 0,0 ${x0},${y2 - hr}`);
        segs.push(`L${x0},${y2 - size + hr}`);
        segs.push(`A${hr},${hr} 0 0,1 ${x0 + hr},${y2 - size}`);
        segs.push(`L${x1 - hr},${y2 - size}`);
        segs.push(`A${hr},${hr} 0 0,1 ${x1},${y2 - size + hr}`);
        segs.push(`L${x1},${y2 - hr}`);
        segs.push(`A${hr},${hr} 0 0,0 ${x1 + hr},${y2}`);
    }

    segs.push(`L${fullW - tailExtension - radius},${y2}`);
    segs.push(`A${radius},${radius} 0 0,0 ${fullW - tailExtension},${y2 - radius}`);

    const midY = y1 + adjustedDrop / 2;
    const dy = y2 - midY;
    const endR = dy / 2;

    segs.push(`L${fullW - tailExtension},${midY + endR}`);
    segs.push(`A${endR},${endR} 0 0,1 ${fullW + endR - tailExtension},${midY}`);
    segs.push(`L${fullW + endR},${midY}`);

    return segs.join(" ");
}

// Main component with animated path
export default function SectionHeader({
    children,
    lineY = 16,
    dropHeight = 48,
    radius = 8,
    strokeWidth = 15,
    strokeScale = 1,
    color = "#FF79C6",
    textGap = 20,
    rightGap = 25,
    humpCount = 2,
    seed = 42,
}) {
    const { containerRef, textRef, fullW, textW } = useDimensions();
    const pathRef = useRef(null);
    const [pathLength, setPathLength] = useState(0);

    useLayoutEffect(() => {
        if (pathRef.current) {
            setPathLength(pathRef.current.getTotalLength());
        }
    }, [fullW, textW]);

    const windowWidth = typeof window !== "undefined" ? window.innerWidth : 1024;

    const isSmall = windowWidth < 640;
    const isMedium = windowWidth >= 640 && windowWidth < 1024;

    if (fullW === 0 || isSmall) {
        return (
            <div ref={containerRef} className="relative">
                <h2 ref={textRef} className="inline-block text-3xl font-bold text-gray-800">
                    {children}
                </h2>
            </div>
        );
    }

    const adjustedHumps = isMedium ? 1 : humpCount;
    const adjustedRightGap = isMedium ? rightGap / 2 : rightGap;

    const bottomPad = dropHeight + textGap * 2 + strokeWidth * strokeScale;
    const leftPad = strokeWidth * strokeScale;
    const rightPad = adjustedRightGap + strokeWidth * strokeScale;

    const pathData = generatePathData({
        fullW,
        textW,
        lineY,
        dropHeight,
        textGap,
        rightGap: adjustedRightGap,
        radius,
        strokeScale,
        humpCount: adjustedHumps,
        seed,
    });

    const svgHeight = lineY + dropHeight + textGap * 2 + strokeWidth * strokeScale * 2;
    const totalWidth = fullW + rightPad;
    return (
        <div
            ref={containerRef}
            className="relative"
            style={{
                paddingBottom: bottomPad,
                paddingLeft: leftPad,
                paddingRight: rightPad,
            }}
        >
            <svg
                className="absolute inset-0"
                width={totalWidth}
                height={svgHeight}
                viewBox={`0 0 ${totalWidth} ${svgHeight}`}
                fill="none"
                style={{ pointerEvents: "none" }}
            >
                <path
                    ref={pathRef}
                    d={pathData}
                    stroke={color}
                    strokeWidth={strokeWidth * strokeScale}
                    fill="none"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    style={{
                        strokeDasharray: pathLength,
                        strokeDashoffset: pathLength,
                        animation: "dash-animation 10s ease-in-out infinite",
                        animationDelay: `-${(Math.random() * 10).toFixed(2)}s`
                    }}
                />
            </svg>

            <h2
                ref={textRef}
                className="relative inline-block text-3xl font-bold text-gray-800"
                style={{
                    paddingTop: lineY + strokeWidth * strokeScale + textGap,
                    marginBottom: textGap + strokeWidth * strokeScale,
                    paddingRight: textGap,
                }}
            >
                {children}
            </h2>

            {/* CSS Animation */}
            <style>{`
                @keyframes dash-animation {
                    0% {
                        stroke-dashoffset: -100%;
                    }
                    25% {
                        stroke-dashoffset: 0%;
                    }
                    50% {
                        stroke-dashoffset: 100%;
                    }
                    75%{
                        stroke-dashoffset: 0%;
                    }
                    100%{
                        stroke-dashoffset: -100%;
                    }
                }
            `}</style>
        </div>
    );
}
