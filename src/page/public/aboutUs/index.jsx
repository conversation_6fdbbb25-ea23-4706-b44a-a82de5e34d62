import { useTranslation } from "react-i18next";
import SectionHeader from "shared/ui/sectionHeader/SectionHeader";
function AboutUs() {
    const { t } = useTranslation();

    return (
        <div className="min-h-screen  text-white">
            {/* Hero Section */}
            <div className="relative">
                {/* Gradient Background Section */}
                <div className="pt-32 border-2 pb-32 text-center bg-gradient-to-br from-purple-600 via-purple-500 to-indigo-500">
                    <h1 className="text-4xl md:text-6xl font-bold mb-6">
                        {t('about.title') || 'О нас'}
                    </h1>
                    <p className="text-lg md:text-xl text-white/90 max-w-3xl mx-auto leading-relaxed pb-32">
                        {/* Translate it */}
                        Современная платформа для аренды жилья с системой умных замков —
                        безопасно, удобно и доступно для всех.
                    </p>
                </div>
                {/* Video Section - shifted upward so background appears behind half */}
                <div className="relative top-5 -mt-40 mx-auto w-1/2 z-10">
                    <video
                        src="/videos/ToGolock.mp4"
                        autoPlay
                        muted
                        loop
                        playsInline
                        className="rounded-md w-full h-auto"
                    />
                </div>
            </div>

            <div className="container mx-auto px-4 py-16">
                {/* Main content sections */}
                <div className="max-w-4xl mx-auto space-y-16">
                    {/* Who are we section */}
                    <div>
                        <div className="bg-white rounded-2xl p-8 md:p-12">
                            <SectionHeader
                                strokeWidth={25}
                                textGap={10}
                                rightGap={40}
                                color="#F36A7B"
                            >Кто мы?</SectionHeader>
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="w-full">
                                    <img src="/3dkey.png" className="w-full h-auto" alt="" />
                                </div>
                                <div className="text-gray-700 text-lg leading-relaxed">
                                    <p dangerouslySetInnerHTML={{ __html: t('about.description.p1') || 'ToGoLock — это современное решение для краткосрочной аренды жилья с интегрированной системой умных замков.' }} />
                                </div>
                            </div>
                        </div>
                    </div>


                    {/* What's our specialty section */}
                    <div className="relative">
                        <div className="bg-white rounded-2xl p-8 md:p-12">
                            <SectionHeader color="#6A00FF"
                                strokeWidth={20}
                                humpCount={1} >
                                В чем наша особенность?
                            </SectionHeader>
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="text-gray-700 text-lg leading-relaxed">
                                    <p >Наша система умных замков позволяет легко и безопасно заселиться в жильё — доступ выдается через мобильное приложение. Всё прозрачно, без лишних контактов и бумажной волокиты.</p>
                                </div>
                                <div className="w-11/12">
                                    <img src="/3dlock.png" className="w-full h-auto" alt="" />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Who is TOGOLOCK created for section */}
                    <div className="relative">
                        <div className="bg-white rounded-2xl p-8 md:p-12">
                            <SectionHeader color="#FF3149"
                                humpCount={1}><>Для кого <br />создан TOGOLOCK?</>
                            </SectionHeader>
                            <div className="flex flex-col md:flex-row items-center gap-8">
                                <div className="w-11/12">
                                    <img src="/3dhouse.png" className="w-full h-auto" alt="" />
                                </div>
                                <div className="text-gray-700 text-lg leading-relaxed">
                                    <ul className="space-y-2">
                                        <li className="flex items-center">
                                            <span className="w-5 h-2 bg-purple-500 rounded-full mr-3"></span>
                                            Для владельцев недвижимости, которые хотят сдавать жильё дистанционно, без показов, звонков и передачи ключей.
                                        </li>
                                        <li className="flex items-center">
                                            <span className="w-5 h-2 bg-purple-500 rounded-full mr-3"></span>
                                            Для арендаторов, ищущих понятный, безопасный и полностью онлайн-сервис без посредников и неприятных сюрпризов.
                                        </li>
                                    </ul>
                                    <p>Мы подходим тем, кто ценит удобство, автоматизацию и прозрачность в аренде — с любой стороны сделки.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default AboutUs;
