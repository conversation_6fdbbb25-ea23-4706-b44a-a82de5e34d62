export default function generatePathData({
    fullW,
    textW,
    lineY,
    dropHeight,
    textGap,
    rightGap,
    radius,
    humpCount,
}) {
    const y1 = lineY;
    const adjustedDrop = dropHeight + textGap * 2;
    const y2 = y1 + adjustedDrop;

    // Start of path before humps
    const startX = textW + textGap + rightGap;
    const segs = [
        `M0,${y1}`,
        `L${startX - radius},${y1}`,
        `A${radius},${radius} 0 0,1 ${startX},${y1 + radius}`,
        `L${startX},${y2 - radius}`,
        `A${radius},${radius} 0 0,0 ${startX + radius},${y2}`,
    ];

    const tailExtension = Math.random() * 105; // Extend tail to the left

    // Compute available bottom line for humps
    const areaStart = startX + radius;
    const areaEnd = fullW - tailExtension - radius;
    const availableWidth = areaEnd - areaStart;
    const segmentWidth = availableWidth / humpCount;

    for (let i = 0; i < humpCount; i++) {
        const segStart = areaStart + i * segmentWidth;
        const minSize = Math.min(adjustedDrop * 0.2, segmentWidth * 0.2);
        const maxSize = Math.min(adjustedDrop, segmentWidth);
        const size = Math.random() * (maxSize - minSize) + minSize;
        const hr = Math.min(radius, size / 4);
        const maxOffset = Math.max(0, segmentWidth - size);
        const offset = Math.random() * maxOffset;
        const x0 = segStart + offset;
        const x1 = x0 + size;

        segs.push(`L${x0 - hr},${y2}`);
        segs.push(`A${hr},${hr} 0 0,0 ${x0},${y2 - hr}`);
        segs.push(`L${x0},${y2 - size + hr}`);
        segs.push(`A${hr},${hr} 0 0,1 ${x0 + hr},${y2 - size}`);
        segs.push(`L${x1 - hr},${y2 - size}`);
        segs.push(`A${hr},${hr} 0 0,1 ${x1},${y2 - size + hr}`);
        segs.push(`L${x1},${y2 - hr}`);
        segs.push(`A${hr},${hr} 0 0,0 ${x1 + hr},${y2}`);
    }

    // Finish bottom line and tail
    segs.push(`L${fullW - tailExtension - radius},${y2}`);
    segs.push(`A${radius},${radius} 0 0,0 ${fullW - tailExtension},${y2 - radius}`);

    const midY = y1 + adjustedDrop / 2;
    const dy = y2 - midY;
    const endR = dy / 2;

    segs.push(`L${fullW - tailExtension},${midY + endR}`);
    segs.push(`A${endR},${endR} 0 0,1 ${fullW + endR - tailExtension},${midY}`);
    segs.push(`L${fullW + endR},${midY}`);
    return segs.join(" ");
}