// components/Section.jsx
import React from "react";

export default function Section({ children, color = "#FF79C6", height = 40, offset = 0 }) {
    return (
        <div className="relative mb-16">
            {/* full-width SVG stuck to the container */}
            <div
                className="absolute inset-x-0"
                style={{ top: offset }}
            >
                <div className="max-w-4xl mx-auto">
                    <svg
                        className="w-full"
                        viewBox="0 0 400 40"
                        height={height}
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        {/* Stretch this path across the full width */}
                        <path
                            d="M0,20 C100,0 300,40 400,20"
                            stroke={color}
                            strokeWidth="8"
                            strokeLinecap="round"
                        />
                    </svg>
                </div>
            </div>

            {/* card content */}
            <div className="max-w-4xl mx-auto bg-white rounded-2xl p-8 md:p-12">
                {children}
            </div>
        </div>
    );
}